import axios from 'axios';

const URL = import.meta.env.VITE_API_URL;

export interface PropertyManagementKpiApiResponse {
  data: PropertyManagementKpiItem[];
}

export interface PropertyManagementKpiItem {
  parameter: string;
  East?: number;
  West?: number;
  Central?: number;
  Consolidated?: number;
}

export interface PropertyManagementKpiPayload {
  year?: string | string[];
  month?: string | string[];
  region?: string | string[];
  property?: string | string[];
}

const defaultPayload: PropertyManagementKpiPayload = {
  year: '2025',
  month: ['1'],
  region: [],
  property: [],
};

export const getPropertyManagementYoyData = async (
  payload?: PropertyManagementKpiPayload,
): Promise<PropertyManagementKpiApiResponse> => {
  try {
    const requestPayload = payload || defaultPayload;

    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-yoy`,
      requestPayload,
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching Property Management YOY data:', error);
    return {
      data: [],
    };
  }
};

export const getPropertyManagementNoiData = async (
  payload?: PropertyManagementKpiPayload,
): Promise<PropertyManagementKpiApiResponse> => {
  try {
    const requestPayload = payload || defaultPayload;

    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-noi`,
      requestPayload,
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching Property Management NOI data:', error);
    return {
      data: [],
    };
  }
};

export const getPropertyManagementJTurnerData = async (
  payload?: PropertyManagementKpiPayload,
): Promise<PropertyManagementKpiApiResponse> => {
  try {
    const requestPayload = payload || defaultPayload;

    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-jturner`,
      requestPayload,
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching Property Management J Turner data:', error);
    return {
      data: [],
    };
  }
};

export const getAllPropertyManagementKpiData = async (
  payload?: PropertyManagementKpiPayload,
): Promise<{
  yoyData: PropertyManagementKpiApiResponse;
  noiData: PropertyManagementKpiApiResponse;
  jturnerData: PropertyManagementKpiApiResponse;
}> => {
  try {
    const [yoyData, noiData, jturnerData] = await Promise.all([
      getPropertyManagementYoyData(payload),
      getPropertyManagementNoiData(payload),
      getPropertyManagementJTurnerData(payload),
    ]);

    return {
      yoyData,
      noiData,
      jturnerData,
    };
  } catch (error) {
    console.error('Error fetching all Property Management KPI data:', error);
    return {
      yoyData: { data: [] },
      noiData: { data: [] },
      jturnerData: { data: [] },
    };
  }
};
