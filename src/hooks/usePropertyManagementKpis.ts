import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  getAllPropertyManagementKpiData,
  PropertyManagementKpiPayload,
} from '@/api/propertyManagementKpiApi';
import {
  setOccupancyRentsData,
  setOperationalKpisData,
  setPropertyPerformanceData,
  setSectionLoading,
  setSectionError,
  setAllLoading,
  clearAllErrors,
} from '@/slice/propertyManagementKpiSlice';
import {
  transformYoyDataToOccupancyRents,
  transformNoiDataToOperationalKpis,
  transformJTurnerDataToPropertyPerformance,
  getMockOccupancyRentsData,
  getMockOperationalKpisData,
  getMockPropertyPerformanceData,
} from '@/utils/propertyManagementKpiTransformers';

/**
 * Custom hook for managing Property Management KPIs data fetching
 * Implements parallel API calls with progressive loading and component-specific error handling
 */
export const usePropertyManagementKpis = () => {
  const dispatch = useDispatch();
  const { filters, loadingStates, errorStates } = useSelector(
    (state: RootState) => state.propertyManagementKpi,
  );

  /**
   * Convert month names to numbers for API payload
   */
  const getMonthNumbers = useCallback((monthNames: string[]): string[] => {
    const monthMap: Record<string, string> = {
      January: '1',
      February: '2',
      March: '3',
      April: '4',
      May: '5',
      June: '6',
      July: '7',
      August: '8',
      September: '9',
      October: '10',
      November: '11',
      December: '12',
    };

    return monthNames.map((month) => monthMap[month] || '1');
  }, []);

  /**
   * Build API payload from current filters
   */
  const buildApiPayload = useCallback((): PropertyManagementKpiPayload => {
    const monthNumbers = filters.month.length > 0 
      ? getMonthNumbers(filters.month) 
      : [];

    return {
      year: filters.year || '2025',
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      region: filters.region.length === 0 ? [] : filters.region,
      property: filters.property.length === 0 ? [] : filters.property,
    };
  }, [filters, getMonthNumbers]);

  /**
   * Fetch all Property Management KPI data with parallel API calls
   */
  const fetchAllKpiData = useCallback(async () => {
    try {
      // Clear previous errors
      dispatch(clearAllErrors());
      
      // Set all sections to loading
      dispatch(setAllLoading(true));

      const payload = buildApiPayload();

      // Make parallel API calls
      const { yoyData, noiData, jturnerData } = await getAllPropertyManagementKpiData(payload);

      // Process and dispatch YOY data (Occupancy & Rents)
      try {
        const transformedYoyData = yoyData.data.length > 0 
          ? transformYoyDataToOccupancyRents(yoyData.data)
          : getMockOccupancyRentsData();
        
        dispatch(setOccupancyRentsData(transformedYoyData));
        dispatch(setSectionLoading({ section: 'occupancyRents', loading: false }));
      } catch (error) {
        console.error('Error processing YOY data:', error);
        dispatch(setSectionError({ 
          section: 'occupancyRents', 
          error: 'Failed to load Occupancy & Rents data' 
        }));
        dispatch(setSectionLoading({ section: 'occupancyRents', loading: false }));
        // Use mock data as fallback
        dispatch(setOccupancyRentsData(getMockOccupancyRentsData()));
      }

      // Process and dispatch NOI data (Operational KPIs)
      try {
        const transformedNoiData = noiData.data.length > 0 
          ? transformNoiDataToOperationalKpis(noiData.data)
          : getMockOperationalKpisData();
        
        dispatch(setOperationalKpisData(transformedNoiData));
        dispatch(setSectionLoading({ section: 'operationalKpis', loading: false }));
      } catch (error) {
        console.error('Error processing NOI data:', error);
        dispatch(setSectionError({ 
          section: 'operationalKpis', 
          error: 'Failed to load Operational KPIs data' 
        }));
        dispatch(setSectionLoading({ section: 'operationalKpis', loading: false }));
        // Use mock data as fallback
        dispatch(setOperationalKpisData(getMockOperationalKpisData()));
      }

      // Process and dispatch J Turner data (Property Performance)
      try {
        const transformedJTurnerData = jturnerData.data.length > 0 
          ? transformJTurnerDataToPropertyPerformance(jturnerData.data)
          : getMockPropertyPerformanceData();
        
        dispatch(setPropertyPerformanceData(transformedJTurnerData));
        dispatch(setSectionLoading({ section: 'propertyPerformance', loading: false }));
      } catch (error) {
        console.error('Error processing J Turner data:', error);
        dispatch(setSectionError({ 
          section: 'propertyPerformance', 
          error: 'Failed to load Property Performance data' 
        }));
        dispatch(setSectionLoading({ section: 'propertyPerformance', loading: false }));
        // Use mock data as fallback
        dispatch(setPropertyPerformanceData(getMockPropertyPerformanceData()));
      }

    } catch (error) {
      console.error('Error fetching Property Management KPI data:', error);
      
      // Set errors for all sections
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch data';
      dispatch(setSectionError({ section: 'occupancyRents', error: errorMessage }));
      dispatch(setSectionError({ section: 'operationalKpis', error: errorMessage }));
      dispatch(setSectionError({ section: 'propertyPerformance', error: errorMessage }));
      
      // Use mock data as fallback
      dispatch(setOccupancyRentsData(getMockOccupancyRentsData()));
      dispatch(setOperationalKpisData(getMockOperationalKpisData()));
      dispatch(setPropertyPerformanceData(getMockPropertyPerformanceData()));
    } finally {
      // Ensure all loading states are cleared
      dispatch(setAllLoading(false));
    }
  }, [dispatch, buildApiPayload]);

  /**
   * Fetch data when filters change
   */
  useEffect(() => {
    fetchAllKpiData();
  }, [fetchAllKpiData]);

  /**
   * Check if any section is currently loading
   */
  const isAnyLoading = loadingStates.occupancyRents || 
                      loadingStates.operationalKpis || 
                      loadingStates.propertyPerformance;

  /**
   * Check if any section has errors
   */
  const hasAnyError = errorStates.occupancyRents || 
                      errorStates.operationalKpis || 
                      errorStates.propertyPerformance;

  return {
    loadingStates,
    errorStates,
    isAnyLoading,
    hasAnyError,
    refetch: fetchAllKpiData,
  };
};
