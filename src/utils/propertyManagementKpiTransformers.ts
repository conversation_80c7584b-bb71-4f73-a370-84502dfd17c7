import {
  OccupancyRentsItem,
  OperationalKpiItem,
  PropertyPerformanceItem,
} from '@/slice/propertyManagementKpiSlice';
import { PropertyManagementKpiItem } from '@/api/propertyManagementKpiApi';

/**
 * Format numeric values to display with 1 decimal place for percentages
 */
const formatPercentage = (value: number): string => {
  return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
};

/**
 * Format currency values with proper formatting
 */
const formatCurrency = (value: number): string => {
  if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}k`;
  }
  return `$${value.toFixed(0)}`;
};

/**
 * Format cost per turn values (special formatting for AVG COST PER TURN)
 */
const formatCostPerTurn = (value: number): string => {
  return formatCurrency(value * 1000); // Convert from decimal to actual dollar amount
};

/**
 * Transform YOY API data to Occupancy & Rents table structure
 */
export const transformYoyDataToOccupancyRents = (
  apiData: PropertyManagementKpiItem[],
): OccupancyRentsItem[] => {
  return apiData.map((item) => ({
    category: item.parameter,
    central: {
      actualVsBudget: formatPercentage(item.Central || 0),
      actualVsSubmarket: formatPercentage((item.Central || 0) + 1.5), // Mock calculation for submarket
    },
    east: {
      actualVsBudget: formatPercentage(item.East || 0),
      actualVsSubmarket: formatPercentage((item.East || 0) + 1.2), // Mock calculation for submarket
    },
    west: {
      actualVsBudget: formatPercentage(item.West || 0),
      actualVsSubmarket: formatPercentage((item.West || 0) + 0.8), // Mock calculation for submarket
    },
    consolidated: {
      actualVsBudget: formatPercentage(item.Consolidated || 0),
      actualVsMarket: formatPercentage((item.Consolidated || 0) + 1.1), // Mock calculation for market
    },
  }));
};

/**
 * Transform NOI API data to Operational KPIs table structure
 */
export const transformNoiDataToOperationalKpis = (
  apiData: PropertyManagementKpiItem[],
): OperationalKpiItem[] => {
  return apiData.map((item) => {
    const parameter = item.parameter;
    
    // Handle different parameter types with appropriate formatting
    if (parameter.toLowerCase().includes('cost') || parameter.toLowerCase().includes('turn')) {
      return {
        category: parameter,
        central: {
          actual: formatCostPerTurn(item.Central || 0),
          target: formatCostPerTurn((item.Central || 0) * 0.95), // Mock target calculation
        },
        east: {
          actual: formatCostPerTurn(item.East || 0),
          target: formatCostPerTurn((item.East || 0) * 0.95),
        },
        west: {
          actual: formatCostPerTurn(item.West || 0),
          target: formatCostPerTurn((item.West || 0) * 0.95),
        },
        consolidated: {
          actual: formatCostPerTurn(item.Consolidated || 0),
          target: formatCostPerTurn((item.Consolidated || 0) * 0.95),
        },
      };
    } else if (parameter.toLowerCase().includes('noi')) {
      return {
        category: parameter,
        central: {
          actual: formatPercentage(item.Central || 0),
          target: formatPercentage((item.Central || 0) * 0.9), // Mock target calculation
        },
        east: {
          actual: formatPercentage(item.East || 0),
          target: formatPercentage((item.East || 0) * 0.9),
        },
        west: {
          actual: formatPercentage(item.West || 0),
          target: formatPercentage((item.West || 0) * 0.9),
        },
        consolidated: {
          actual: formatPercentage(item.Consolidated || 0),
          target: formatPercentage((item.Consolidated || 0) * 0.9),
        },
      };
    } else {
      // Default formatting for other parameters
      return {
        category: parameter,
        central: {
          actual: (item.Central || 0).toFixed(1),
          target: ((item.Central || 0) * 0.95).toFixed(1), // Mock target calculation
        },
        east: {
          actual: (item.East || 0).toFixed(1),
          target: ((item.East || 0) * 0.95).toFixed(1),
        },
        west: {
          actual: (item.West || 0).toFixed(1),
          target: ((item.West || 0) * 0.95).toFixed(1),
        },
        consolidated: {
          actual: (item.Consolidated || 0).toFixed(1),
          target: ((item.Consolidated || 0) * 0.95).toFixed(1),
        },
      };
    }
  });
};

/**
 * Transform J Turner API data to Property Performance table structure
 */
export const transformJTurnerDataToPropertyPerformance = (
  apiData: PropertyManagementKpiItem[],
): PropertyPerformanceItem[] => {
  return apiData.map((item) => ({
    lineItem: item.parameter,
    central: formatPercentage(item.Central || 0),
    east: formatPercentage(item.East || 0),
    west: formatPercentage(item.West || 0),
    consolidated: formatPercentage(item.Consolidated || 0),
  }));
};

/**
 * Get mock data for testing when API returns empty results
 */
export const getMockOccupancyRentsData = (): OccupancyRentsItem[] => [
  {
    category: 'Occupancy',
    central: { actualVsBudget: '-2.0%', actualVsSubmarket: '+2.0%' },
    east: { actualVsBudget: '-2.0%', actualVsSubmarket: '+2.0%' },
    west: { actualVsBudget: '-2.0%', actualVsSubmarket: '+2.0%' },
    consolidated: { actualVsBudget: '-2.0%', actualVsMarket: '+2.0%' },
  },
  {
    category: 'Renewal In Place Rent YOY Change',
    central: { actualVsBudget: '+3.7%', actualVsSubmarket: '+5.2%' },
    east: { actualVsBudget: '+6.9%', actualVsSubmarket: '+8.1%' },
    west: { actualVsBudget: '+6.1%', actualVsSubmarket: '+7.3%' },
    consolidated: { actualVsBudget: '+5.6%', actualVsMarket: '+6.7%' },
  },
];

export const getMockOperationalKpisData = (): OperationalKpiItem[] => [
  {
    category: 'Controllable NOI',
    central: { actual: '+15.5%', target: '+14.0%' },
    east: { actual: '+1.9%', target: '+1.8%' },
    west: { actual: '+10.4%', target: '+9.9%' },
    consolidated: { actual: '+6.7%', target: '+6.4%' },
  },
];

export const getMockPropertyPerformanceData = (): PropertyPerformanceItem[] => [
  {
    lineItem: 'COST Per Turn',
    central: '+0.2%',
    east: '+0.2%',
    west: '+0.2%',
    consolidated: '+0.3%',
  },
];
