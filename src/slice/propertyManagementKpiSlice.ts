import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Regional data interface for occupancy and rents
export interface RegionalOccupancyData {
  actualVsBudget: string;
  actualVsSubmarket?: string;
  actualVsMarket?: string;
}

// Data interface for Occupancy & Rents tab
export interface OccupancyRentsItem {
  category: string;
  central: RegionalOccupancyData;
  east: RegionalOccupancyData;
  west: RegionalOccupancyData;
  consolidated: RegionalOccupancyData;
}

// Regional data interface for operational KPIs
export interface RegionalOperationalData {
  actual: string | number;
  target: string | number;
}

// Data interface for Operational KPIs tab
export interface OperationalKpiItem {
  category: string;
  central: RegionalOperationalData;
  east: RegionalOperationalData;
  west: RegionalOperationalData;
  consolidated: RegionalOperationalData;
}

// Data interface for Property Performance tab
export interface PropertyPerformanceItem {
  lineItem: string;
  central: string;
  east: string;
  west: string;
  consolidated: string;
}

// Filters interface
export interface PropertyManagementKpiFilters {
  year: string;
  month: string[];
  region: string[];
  property: string[];
}

export interface FilterOption {
  value: string;
  label: string;
}

// Loading states for component-specific indicators
export interface LoadingStates {
  occupancyRents: boolean;
  operationalKpis: boolean;
  propertyPerformance: boolean;
  filterOptions: boolean;
}

// Error states for component-specific error handling
export interface ErrorStates {
  occupancyRents: string | null;
  operationalKpis: string | null;
  propertyPerformance: string | null;
  filterOptions: string | null;
}

// Main state interface
export interface PropertyManagementKpiState {
  activeTab: 'occupancyRents' | 'operationalKpis' | 'propertyPerformance';
  filters: PropertyManagementKpiFilters;
  occupancyRentsData: OccupancyRentsItem[];
  operationalKpisData: OperationalKpiItem[];
  propertyPerformanceData: PropertyPerformanceItem[];
  availableRegions: FilterOption[];
  availableProperties: FilterOption[];
  loading: boolean;
  error: string | null;
  loadingStates: LoadingStates;
  errorStates: ErrorStates;
}

// Initial state with mock data matching the images
const initialState: PropertyManagementKpiState = {
  activeTab: 'occupancyRents',
  filters: {
    year: '2025',
    month: ['January'], // Default to January (month "1")
    region: [],
    property: [],
  },
  occupancyRentsData: [
    {
      category: 'Occupancy',
      central: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      east: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      west: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      consolidated: { actualVsBudget: '-2%', actualVsMarket: '+2%' },
    },
    {
      category: 'New in Place Rent YoY Change',
      central: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      east: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      west: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      consolidated: { actualVsBudget: '-2%', actualVsMarket: '+2%' },
    },
    {
      category: 'Renewal in Place Rent YoY Change',
      central: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      east: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      west: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      consolidated: { actualVsBudget: '-2%', actualVsMarket: '+2%' },
    },
    {
      category: 'In Place Rent YoY Change',
      central: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      east: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      west: { actualVsBudget: '-2%', actualVsSubmarket: '+2%' },
      consolidated: { actualVsBudget: '-2%', actualVsMarket: '+2%' },
    },
  ],
  operationalKpisData: [
    {
      category: 'Avg. Cost/Turn',
      central: { actual: '$590', target: '$575' },
      east: { actual: '$590', target: '$575' },
      west: { actual: '$590', target: '$575' },
      consolidated: { actual: '$590', target: '$575' },
    },
    {
      category: 'J. Turner Score',
      central: { actual: '95%', target: '95%' },
      east: { actual: '95%', target: '95%' },
      west: { actual: '95%', target: '95%' },
      consolidated: { actual: '95%', target: '95%' },
    },
    {
      category: 'Google',
      central: { actual: '5 stars', target: '4 stars' },
      east: { actual: '5 stars', target: '4 stars' },
      west: { actual: '5 stars', target: '4 stars' },
      consolidated: { actual: '5 stars', target: '4 stars' },
    },
    {
      category: 'Collections % MTD',
      central: { actual: '96.5%', target: '94.0%' },
      east: { actual: '96.5%', target: '94.0%' },
      west: { actual: '96.5%', target: '94.0%' },
      consolidated: { actual: '96.5%', target: '94.0%' },
    },
    {
      category: 'Average Unit Turn Time (in Days)',
      central: { actual: 7, target: 7 },
      east: { actual: 7, target: 7 },
      west: { actual: 7, target: 7 },
      consolidated: { actual: 7, target: 7 },
    },
    {
      category: 'Units with Repeat Service Tickets in 30 Days',
      central: { actual: 3, target: 3 },
      east: { actual: 3, target: 3 },
      west: { actual: 3, target: 3 },
      consolidated: { actual: 3, target: 3 },
    },
    {
      category: '% Service Tickets Outstanding > 24 Hours',
      central: { actual: '1.0%', target: '2.0%' },
      east: { actual: '1.0%', target: '2.0%' },
      west: { actual: '1.0%', target: '2.0%' },
      consolidated: { actual: '1.0%', target: '2.0%' },
    },
  ],
  propertyPerformanceData: [
    {
      lineItem: 'Income',
      central: '+0.65%',
      east: '+0.65%',
      west: '+0.65%',
      consolidated: '+0.65%',
    },
    {
      lineItem: 'Controllable Opex',
      central: '+0.65%',
      east: '+0.65%',
      west: '+0.65%',
      consolidated: '+0.65%',
    },
    {
      lineItem: 'Total Opex',
      central: '+0.65%',
      east: '+0.65%',
      west: '+0.65%',
      consolidated: '+0.65%',
    },
    {
      lineItem: 'NOI',
      central: '+0.65%',
      east: '+0.65%',
      west: '+0.65%',
      consolidated: '+0.65%',
    },
    {
      lineItem: 'Controllable NOI',
      central: '+0.65%',
      east: '+0.65%',
      west: '+0.65%',
      consolidated: '+0.65%',
    },
  ],
  availableRegions: [
    { value: 'central', label: 'Central' },
    { value: 'east', label: 'East' },
    { value: 'west', label: 'West' },
    { value: 'consolidated', label: 'Consolidated' },
  ],
  availableProperties: [],
  loading: false,
  error: null,
  loadingStates: {
    occupancyRents: false,
    operationalKpis: false,
    propertyPerformance: false,
    filterOptions: false,
  },
  errorStates: {
    occupancyRents: null,
    operationalKpis: null,
    propertyPerformance: null,
    filterOptions: null,
  },
};

// Create slice
const propertyManagementKpiSlice = createSlice({
  name: 'propertyManagementKpi',
  initialState,
  reducers: {
    setActiveTab: (
      state,
      action: PayloadAction<
        'occupancyRents' | 'operationalKpis' | 'propertyPerformance'
      >,
    ) => {
      state.activeTab = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<PropertyManagementKpiFilters>>,
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setOccupancyRentsData: (
      state,
      action: PayloadAction<OccupancyRentsItem[]>,
    ) => {
      state.occupancyRentsData = action.payload;
    },
    setOperationalKpisData: (
      state,
      action: PayloadAction<OperationalKpiItem[]>,
    ) => {
      state.operationalKpisData = action.payload;
    },
    setPropertyPerformanceData: (
      state,
      action: PayloadAction<PropertyPerformanceItem[]>,
    ) => {
      state.propertyPerformanceData = action.payload;
    },
    setAvailableRegions: (state, action: PayloadAction<FilterOption[]>) => {
      state.availableRegions = action.payload;
    },
    setAvailableProperties: (state, action: PayloadAction<FilterOption[]>) => {
      state.availableProperties = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    resetFilters: (state) => {
      state.filters = {
        year: '2025',
        month: ['January'], // Default to January (month "1")
        region: [],
        property: [],
      };
    },
    setSectionLoading: (
      state,
      action: PayloadAction<{ section: keyof LoadingStates; loading: boolean }>,
    ) => {
      state.loadingStates[action.payload.section] = action.payload.loading;
    },
    setSectionError: (
      state,
      action: PayloadAction<{
        section: keyof ErrorStates;
        error: string | null;
      }>,
    ) => {
      state.errorStates[action.payload.section] = action.payload.error;
    },
    clearAllErrors: (state) => {
      state.error = null;
      state.errorStates = {
        occupancyRents: null,
        operationalKpis: null,
        propertyPerformance: null,
        filterOptions: null,
      };
    },
    setAllLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
      state.loadingStates = {
        occupancyRents: action.payload,
        operationalKpis: action.payload,
        propertyPerformance: action.payload,
        filterOptions: action.payload,
      };
    },
  },
});

// Export actions
export const {
  setActiveTab,
  setFilters,
  setOccupancyRentsData,
  setOperationalKpisData,
  setPropertyPerformanceData,
  setAvailableRegions,
  setAvailableProperties,
  setLoading,
  setError,
  resetFilters,
  setSectionLoading,
  setSectionError,
  clearAllErrors,
  setAllLoading,
} = propertyManagementKpiSlice.actions;

// Export reducer
export default propertyManagementKpiSlice.reducer;
